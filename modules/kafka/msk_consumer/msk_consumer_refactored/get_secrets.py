"""
AWS Secrets Manager integration for retrieving secrets.
"""
import boto3
import json
import logging

def get_secret(secret_name, region_name="us-east-1"):
    """
    Retrieve a secret from AWS Secrets Manager.

    Args:
        secret_name: Name of the secret in AWS Secrets Manager
        region_name: AWS region where the secret is stored

    Returns:
        dict: The secret data as a dictionary
    """
    client = boto3.client("secretsmanager", region_name=region_name)
    try:
        response = client.get_secret_value(SecretId=secret_name)
        if "SecretString" in response:
            return json.loads(response["SecretString"])
        else:
            raise ValueError("Secret is not in string format.")
    except Exception as e:
        logging.error(f"Error retrieving {secret_name}: {e}")
        return {}
