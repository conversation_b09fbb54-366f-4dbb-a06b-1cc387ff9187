# MSK Consumer Tests

This directory contains comprehensive unit tests for the refactored MSK Consumer codebase.

## Overview

The test suite provides complete coverage of all modules in the MSK Consumer application, using proper mocking to isolate functionality and avoid dependencies on external services.

## Test Structure

```
tests/
├── __init__.py                    # Test package initialization
├── README.md                      # This documentation
├── requirements-test.txt          # Test dependencies
├── run_tests.py                   # Test runner with mocking setup
├── test_get_secrets.py           # Tests for AWS Secrets Manager integration
├── test_secrets_manager.py       # Tests for credential caching
├── test_config_loader.py         # Tests for configuration management
├── test_postgres_utils.py        # Tests for database operations
├── test_postgres_batch_utils.py  # Tests for batch processing
├── test_kafka_utils.py           # Tests for Kafka functionality
├── test_logging_utils.py         # Tests for logging utilities
└── test_main.py                  # Tests for main entry point
```

## Running Tests

### Option 1: Using the Test Runner (Recommended)

```bash
cd modules/kafka/msk_consumer/msk_consumer_refactored/tests
python3 run_tests.py
```

This automatically sets up all necessary mocks and runs all tests.

### Option 2: Using unittest directly

```bash
cd modules/kafka/msk_consumer/msk_consumer_refactored
python3 -m unittest discover tests -v
```

### Option 3: Running individual test files

```bash
cd modules/kafka/msk_consumer/msk_consumer_refactored
python3 -m unittest tests.test_postgres_utils -v
```

## Test Coverage

### test_get_secrets.py
- ✅ Successful secret retrieval
- ✅ Error handling for missing secrets
- ✅ Invalid JSON handling
- ✅ ClientError exception handling
- ✅ Default and custom region parameters

### test_secrets_manager.py
- ✅ Credential caching functionality
- ✅ Independent caching for Kafka and Postgres credentials
- ✅ First call vs. cached call behavior
- ✅ Error handling when secret retrieval fails

### test_config_loader.py
- ✅ Configuration file loading (success and failure cases)
- ✅ Local schema file loading
- ✅ Schema registry integration
- ✅ Configuration value resolution with priority
- ✅ Process name generation with instance numbering
- ✅ Discarded messages filename generation

### test_postgres_utils.py
- ✅ Database connection pool management
- ✅ Table column and primary key retrieval with caching
- ✅ Decimal type processing and conversion
- ✅ Date/timestamp field conversion
- ✅ Avro message processing pipeline
- ✅ Retry logic for database operations

### test_postgres_batch_utils.py
- ✅ Data serialization and adaptation for PostgreSQL
- ✅ Batch insertion with fallback to individual records
- ✅ Error handling and DLQ integration
- ✅ Discarded message logging
- ✅ Variable scale decimal processing

### test_kafka_utils.py
- ✅ DLQ topic management and message sending
- ✅ Consumer configuration and message polling
- ✅ Schema loading (local files vs. registry)
- ✅ Message processing pipeline integration
- ✅ Error handling and logging

### test_logging_utils.py
- ✅ Custom JSON encoding for complex types
- ✅ Database and file logging for discarded messages
- ✅ Error handling for logging failures
- ✅ Configuration-based logging enablement

### test_main.py
- ✅ Command-line argument parsing
- ✅ Integration with config loading and consumer
- ✅ Boolean flag handling
- ✅ Error handling for missing arguments

## Mocking Strategy

The tests use comprehensive mocking to isolate functionality:

### External Dependencies Mocked:
- **AWS Services**: `boto3`, `botocore.exceptions`
- **Kafka**: `confluent_kafka`, `confluent_kafka.admin`, `confluent_kafka.schema_registry`
- **Database**: `psycopg2`, `psycopg2.pool`, `psycopg2.extras`
- **Data Processing**: `fastavro`
- **System**: `psutil`, `setproctitle`

### Mock Data Provided:
- Realistic AWS Secrets Manager responses
- Sample Avro schemas and messages
- Database connection and cursor behaviors
- Kafka consumer and producer interactions

## Test Features

### Comprehensive Error Testing
- Network failures and timeouts
- Invalid data formats
- Missing configuration files
- Database connection errors
- Schema registry failures

### Edge Case Coverage
- Empty batches and null values
- Large decimal numbers and dates
- Complex nested data structures
- Concurrent process naming
- Cache invalidation scenarios

### Integration Testing
- End-to-end message processing pipeline
- Configuration loading and validation
- Error propagation and handling
- Logging and monitoring integration

## Best Practices

### Test Isolation
- Each test is independent and can run in any order
- Mocks are reset between tests
- No shared state between test cases

### Realistic Scenarios
- Tests use realistic data formats and sizes
- Error conditions mirror real-world failures
- Configuration matches production patterns

### Maintainability
- Clear test names describing the scenario
- Comprehensive docstrings explaining test purpose
- Modular setup and teardown methods

## Adding New Tests

When adding new functionality:

1. **Create test file**: Follow naming pattern `test_<module_name>.py`
2. **Add test class**: Inherit from `unittest.TestCase`
3. **Mock dependencies**: Use `@patch` decorators for external calls
4. **Test scenarios**: Cover success, failure, and edge cases
5. **Update documentation**: Add test coverage to this README

## Troubleshooting

### Common Issues

**Import Errors**: Ensure you're running tests from the correct directory and that the parent directory is in the Python path.

**Mock Failures**: Check that all external dependencies are properly mocked in the test setup.

**Test Isolation**: If tests fail when run together but pass individually, check for shared state or incomplete mocking.

### Debug Mode

For detailed debugging, run tests with:

```bash
python3 -m unittest tests.test_module_name.TestClass.test_method -v
```
