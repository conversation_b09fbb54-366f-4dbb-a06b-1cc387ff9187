#!/usr/bin/env python3
"""
Unit tests for logging_utils.py module.
Tests logging functionality and discarded message handling.
"""

import unittest
from unittest.mock import patch, mock_open, MagicMock
from decimal import Decimal
from datetime import datetime
import json
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestLoggingUtils(unittest.TestCase):
    """Test cases for logging_utils module."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_message = {
            'id': 1,
            'name': 'test',
            'bytes_field': b'\x01\x02\x03',
            'decimal_field': Decimal('123.45'),
            'datetime_field': datetime(2021, 1, 1, 12, 0, 0)
        }

    def test_bytes_encoder_bytes(self):
        """Test BytesEncoder with bytes object."""
        from logging_utils import BytesEncoder
        
        encoder = BytesEncoder()
        test_bytes = b'\x01\x02\x03'
        
        result = encoder.default(test_bytes)
        
        self.assertEqual(result, '010203')

    def test_bytes_encoder_decimal(self):
        """Test BytesEncoder with Decimal object."""
        from logging_utils import BytesEncoder
        
        encoder = BytesEncoder()
        test_decimal = Decimal('123.45')
        
        result = encoder.default(test_decimal)
        
        self.assertEqual(result, 123.45)

    def test_bytes_encoder_datetime(self):
        """Test BytesEncoder with datetime object."""
        from logging_utils import BytesEncoder
        
        encoder = BytesEncoder()
        test_datetime = datetime(2021, 1, 1, 12, 0, 0)
        
        result = encoder.default(test_datetime)
        
        self.assertEqual(result, '2021-01-01T12:00:00')

    def test_bytes_encoder_object_with_str(self):
        """Test BytesEncoder with object that has __str__ method."""
        from logging_utils import BytesEncoder
        
        class TestObject:
            def __str__(self):
                return "test_object_string"
        
        encoder = BytesEncoder()
        test_obj = TestObject()
        
        result = encoder.default(test_obj)
        
        self.assertEqual(result, "test_object_string")

    def test_make_json_serializable_dict(self):
        """Test make_json_serializable with dictionary."""
        from logging_utils import make_json_serializable
        
        result = make_json_serializable(self.test_message)
        
        self.assertEqual(result['id'], 1)
        self.assertEqual(result['name'], 'test')
        self.assertEqual(result['bytes_field'], '010203')
        self.assertEqual(result['decimal_field'], 123.45)
        self.assertEqual(result['datetime_field'], '2021-01-01T12:00:00')

    def test_make_json_serializable_list(self):
        """Test make_json_serializable with list."""
        from logging_utils import make_json_serializable
        
        test_list = [1, 'string', b'\x01\x02', Decimal('123.45')]
        
        result = make_json_serializable(test_list)
        
        self.assertEqual(result, [1, 'string', '0102', 123.45])

    def test_make_json_serializable_nested(self):
        """Test make_json_serializable with nested structures."""
        from logging_utils import make_json_serializable
        
        test_nested = {
            'list_field': [b'\x01\x02', Decimal('123.45')],
            'dict_field': {
                'bytes': b'\x03\x04',
                'decimal': Decimal('67.89')
            }
        }
        
        result = make_json_serializable(test_nested)
        
        self.assertEqual(result['list_field'], ['0102', 123.45])
        self.assertEqual(result['dict_field']['bytes'], '0304')
        self.assertEqual(result['dict_field']['decimal'], 67.89)

    def test_make_json_serializable_none_and_primitives(self):
        """Test make_json_serializable with None and primitive types."""
        from logging_utils import make_json_serializable
        
        test_data = {
            'none_field': None,
            'string_field': 'test',
            'int_field': 123,
            'float_field': 123.45,
            'bool_field': True
        }
        
        result = make_json_serializable(test_data)
        
        self.assertEqual(result, test_data)  # Should be unchanged

    @patch('logging_utils.db_connection')
    def test_log_discarded_message_db_success(self, mock_db_conn):
        """Test successful database logging of discarded message."""
        from logging_utils import log_discarded_message_db
        
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_db_conn.return_value.__enter__.return_value = mock_conn
        
        log_discarded_message_db(
            self.test_message,
            "Test error",
            "test-topic",
            12345
        )
        
        mock_cursor.execute.assert_called_once()
        mock_conn.commit.assert_called_once()
        
        # Check that the execute was called with correct parameters
        call_args = mock_cursor.execute.call_args
        self.assertIn("INSERT INTO ops.discarded_messages", call_args[0][0])

    @patch('logging_utils.db_connection')
    def test_log_discarded_message_db_failure(self, mock_db_conn):
        """Test database logging failure handling."""
        from logging_utils import log_discarded_message_db
        
        mock_db_conn.side_effect = Exception("Database connection failed")
        
        with patch('logging_utils.logging') as mock_logging:
            log_discarded_message_db(
                self.test_message,
                "Test error",
                "test-topic",
                12345
            )
            
            mock_logging.error.assert_called_once()

    def test_log_discarded_message_file_success(self):
        """Test successful file logging of discarded message."""
        from logging_utils import log_discarded_message_file
        
        mock_file_content = ""
        
        with patch('builtins.open', mock_open()) as mock_file:
            log_discarded_message_file(
                self.test_message,
                "Test error",
                "test-topic",
                12345,
                "test_discarded.txt"
            )
            
            mock_file.assert_called_once_with("test_discarded.txt", "a", encoding="utf-8")
            mock_file().write.assert_called_once()
            
            # Check that the written content contains expected elements
            written_content = mock_file().write.call_args[0][0]
            self.assertIn("test-topic", written_content)
            self.assertIn("12345", written_content)
            self.assertIn("Test error", written_content)

    def test_log_discarded_message_file_failure(self):
        """Test file logging failure handling."""
        from logging_utils import log_discarded_message_file
        
        with patch('builtins.open', side_effect=Exception("File write failed")):
            with patch('logging_utils.logging') as mock_logging:
                log_discarded_message_file(
                    self.test_message,
                    "Test error",
                    "test-topic",
                    12345,
                    "test_discarded.txt"
                )
                
                mock_logging.error.assert_called()

    def test_get_discarded_messages_filename(self):
        """Test discarded messages filename generation."""
        from logging_utils import get_discarded_messages_filename
        
        result = get_discarded_messages_filename("schema.table_name")
        
        expected = "schema_table_name_discarded_messages.txt"
        self.assertEqual(result, expected)

    def test_get_discarded_messages_filename_multiple_dots(self):
        """Test filename generation with multiple dots."""
        from logging_utils import get_discarded_messages_filename
        
        result = get_discarded_messages_filename("schema.table.with.dots")
        
        expected = "schema_table_with_dots_discarded_messages.txt"
        self.assertEqual(result, expected)

    @patch('logging_utils.log_discarded_message_db')
    @patch('logging_utils.log_discarded_message_file')
    def test_handle_discarded_message_both_enabled(self, mock_log_file, mock_log_db):
        """Test handle_discarded_message with both DB and file logging enabled."""
        from logging_utils import handle_discarded_message
        
        # Mock the config values
        with patch('logging_utils.ENABLE_DISCARDED_MESSAGES_DB', True):
            with patch('logging_utils.ENABLE_DISCARDED_MESSAGES_FILE', True):
                handle_discarded_message(
                    self.test_message,
                    "Test error",
                    "test-topic",
                    12345,
                    "test_discarded.txt"
                )
        
        mock_log_db.assert_called_once_with(self.test_message, "Test error", "test-topic", 12345)
        mock_log_file.assert_called_once_with(self.test_message, "Test error", "test-topic", 12345, "test_discarded.txt")

    @patch('logging_utils.log_discarded_message_db')
    @patch('logging_utils.log_discarded_message_file')
    def test_handle_discarded_message_db_only(self, mock_log_file, mock_log_db):
        """Test handle_discarded_message with only DB logging enabled."""
        from logging_utils import handle_discarded_message
        
        with patch('logging_utils.ENABLE_DISCARDED_MESSAGES_DB', True):
            with patch('logging_utils.ENABLE_DISCARDED_MESSAGES_FILE', False):
                handle_discarded_message(
                    self.test_message,
                    "Test error",
                    "test-topic",
                    12345,
                    "test_discarded.txt"
                )
        
        mock_log_db.assert_called_once()
        mock_log_file.assert_not_called()

    @patch('logging_utils.log_discarded_message_db')
    @patch('logging_utils.log_discarded_message_file')
    def test_handle_discarded_message_file_only(self, mock_log_file, mock_log_db):
        """Test handle_discarded_message with only file logging enabled."""
        from logging_utils import handle_discarded_message
        
        with patch('logging_utils.ENABLE_DISCARDED_MESSAGES_DB', False):
            with patch('logging_utils.ENABLE_DISCARDED_MESSAGES_FILE', True):
                handle_discarded_message(
                    self.test_message,
                    "Test error",
                    "test-topic",
                    12345,
                    "test_discarded.txt"
                )
        
        mock_log_db.assert_not_called()
        mock_log_file.assert_called_once()


if __name__ == '__main__':
    unittest.main()
