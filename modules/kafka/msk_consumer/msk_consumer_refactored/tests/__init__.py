"""
Test package for MSK Consumer refactored code.

This package contains comprehensive unit tests for all modules in the
MSK Consumer refactored codebase, including:

- get_secrets.py - AWS Secrets Manager integration
- secrets_manager.py - Credential caching and management
- config_loader.py - Configuration loading and schema management
- postgres_utils.py - Database operations and data processing
- postgres_batch_utils.py - Batch processing and error handling
- kafka_utils.py - Kafka consumer functionality and DLQ management
- logging_utils.py - Logging and discarded message handling
- main.py - Command-line interface and entry point

All tests use proper mocking to isolate functionality and avoid
dependencies on external services like AWS, Kafka, and PostgreSQL.
"""

__version__ = "1.0.0"
__author__ = "MSK Consumer Team"
