from get_secrets import get_secret

_kafka_credentials = None
_postgres_credentials = None

def get_kafka_credentials():
    global _kafka_credentials
    if _kafka_credentials is None:
        _kafka_credentials = get_secret('msk-uat', 'me-central-1')
    return _kafka_credentials

def get_postgres_credentials():
    global _postgres_credentials
    if _postgres_credentials is None:
        _postgres_credentials = get_secret('cdp-rds-postgres', 'me-central-1')
    return _postgres_credentials