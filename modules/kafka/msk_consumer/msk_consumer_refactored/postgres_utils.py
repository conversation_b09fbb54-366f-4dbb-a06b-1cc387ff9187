import json
import logging
import time
from contextlib import contextmanager
from decimal import <PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from psycopg2.pool import Simple<PERSON>onnectionPool
from psycopg2.extras import execute_values
import psycopg2
import fastavro
import io
from secrets_manager import get_postgres_credentials

# Connection pool will be initialized lazily
connection_pool = None

# Caches for database metadata
cached_table_columns: Dict[str, List[str]] = {}
cached_primary_keys: Dict[str, List[str]] = {}

def _initialize_connection_pool():
    """Initialize the connection pool if not already initialized."""
    global connection_pool
    if connection_pool is None:
        postgres_credentials = get_postgres_credentials()
        postgres_conn = (
            f"dbname={postgres_credentials['kafka_target_db']} "
            f"user={postgres_credentials['dbt_user']} "
            f"password={postgres_credentials['dbt_password']} "
            f"host={postgres_credentials['dbt_host']} port=5432"
        )
        connection_pool = SimpleConnectionPool(1, 10, postgres_conn)

@contextmanager
def db_connection():
    """Context manager for database connections from the pool."""
    _initialize_connection_pool()
    conn = connection_pool.getconn()
    try:
        yield conn
    finally:
        connection_pool.putconn(conn)

def get_table_columns(schema_name: str, table_name: str) -> List[str]:
    """
    Retrieves the column names for a given schema.table from PostgreSQL,
    caching the results for future lookups.
    """
    cache_key = f"{schema_name}.{table_name}"
    if cache_key in cached_table_columns:
        logging.info(f"Using cached table columns for {schema_name}.{table_name}: {cached_table_columns[cache_key]}")
        return cached_table_columns[cache_key]

    query = """
    SELECT column_name
    FROM information_schema.columns
    WHERE table_schema = %s AND table_name = %s
    ORDER BY ordinal_position;
    """
    try:
        with db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (schema_name, table_name))
                columns = [row[0] for row in cursor.fetchall()]
                cached_table_columns[cache_key] = columns
                logging.debug(f"Retrieved table columns for {schema_name}.{table_name}: {columns}")
        return columns
    except Exception as e:
        logging.error(f"Error fetching table columns for {schema_name}.{table_name}: {e}")
        return []

def get_primary_key(schema_name: str, table_name: str, max_retries: int = 3, retry_delay: float = 1.0) -> List[str]:
    """
    Retrieves the primary key column names for a given schema.table from PostgreSQL,
    caching the results for future lookups. Implements retry logic for transient errors.
    """
    cache_key = f"{schema_name}.{table_name}"
    if cache_key in cached_primary_keys:
        logging.debug(f"Using cached primary keys for {schema_name}.{table_name}: {cached_primary_keys[cache_key]}")
        return cached_primary_keys[cache_key]

    query = """
    SELECT a.attname
    FROM pg_index i
    JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
    JOIN pg_class c ON i.indrelid = c.oid
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = %s AND c.relname = %s AND i.indisprimary;
    """

    retries = 0
    last_error = None

    while retries <= max_retries:
        try:
            with db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, (schema_name, table_name))
                    primary_keys = [row[0] for row in cursor.fetchall()]
                    cached_primary_keys[cache_key] = primary_keys
                    logging.debug(f"Retrieved primary keys for {schema_name}.{table_name}: {primary_keys}")
            return primary_keys
        except Exception as e:
            last_error = e
            retries += 1
            if retries <= max_retries:
                retry_delay_adjusted = retry_delay * (2 ** (retries - 1))  # Exponential backoff
                logging.warning(f"Error fetching primary key columns (attempt {retries}/{max_retries}): {e}. Retrying in {retry_delay_adjusted:.2f}s")
                time.sleep(retry_delay_adjusted)
            else:
                logging.error(f"Failed to fetch primary key columns after {max_retries} attempts: {e}")

    # If we've exhausted retries, return an empty list but don't cache the result
    # This allows future calls to try again rather than using a cached empty result
    logging.error(f"Error fetching primary key columns for {schema_name}.{table_name} after {max_retries} retries: {last_error}")
    return []

def get_column_types(schema_name: str, table_name: str) -> Dict[str, str]:
    """
    Retrieves the column types for a given schema.table from PostgreSQL,
    caching the results for future lookups.
    """
    cache_key = f"{schema_name}.{table_name}.types"
    if cache_key in cached_table_columns:
        return cached_table_columns[cache_key]

    query = """
    SELECT column_name, data_type
    FROM information_schema.columns
    WHERE table_schema = %s AND table_name = %s;
    """
    try:
        with db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (schema_name, table_name))
                column_types = {row[0].lower(): row[1] for row in cursor.fetchall()}
                cached_table_columns[cache_key] = column_types
                logging.debug(f"Retrieved column types for {schema_name}.{table_name}: {column_types}")
        return column_types
    except Exception as e:
        logging.error(f"Error fetching column types for {schema_name}.{table_name}: {e}")
        return {}

def fix_decimal_types(schema_dict):
    """
    Recursively modify the schema to handle only decimal logical types,
    leaving other logical types (like date) unchanged.
    Returns tuple of (modified_schema, decimal_fields) where decimal_fields
    contains information about fields that need decimal conversion.
    """
    decimal_fields = {}

    def _process_schema(schema_dict, path=""):
        if isinstance(schema_dict, dict):
            if schema_dict.get("logicalType") == "decimal":
                field_name = schema_dict.get("name", path.split(".")[-1] if path else "")
                decimal_fields[field_name] = {
                    "scale": schema_dict.get("scale", 0),
                    "path": path
                }
                return schema_dict

            if "fields" in schema_dict:
                for field in schema_dict["fields"]:
                    field_path = f"{path}.{field['name']}" if path else field['name']
                    _process_schema(field, field_path)

            if "type" in schema_dict:
                if isinstance(schema_dict["type"], list):
                    for t in schema_dict["type"]:
                        if isinstance(t, dict):
                            _process_schema(t, path)
                elif isinstance(schema_dict["type"], dict):
                    _process_schema(schema_dict["type"], path)

        return schema_dict

    modified_schema = _process_schema(schema_dict.copy())
    return modified_schema, decimal_fields

def parse_schema_with_decimal(schema_dict):
    """
    Fixes the schema for decimal logical types and parses it using fastavro.
    Returns the parsed schema and decimal field information.
    """
    modified_schema, decimal_fields = fix_decimal_types(schema_dict)
    return fastavro.parse_schema(modified_schema), decimal_fields

def decode_avro_decimal(avro_bytes, scale):
    """
    Decodes an Avro decimal from its bytes representation.
    Avro stores decimals as big-endian signed integers.
    """
    if avro_bytes is None:
        return None

    int_value = int.from_bytes(avro_bytes, byteorder="big", signed=True)

    decimal_value = Decimal(int_value) / (10 ** scale)
    return decimal_value

def decode_variable_scale_decimal(decimal_dict):
    """
    Decode a VariableScaleDecimal from Debezium.
    Format: {'scale': <scale>, 'value': <bytes>}
    """
    if not isinstance(decimal_dict, dict) or 'scale' not in decimal_dict or 'value' not in decimal_dict:
        logging.debug(f"Not a valid VariableScaleDecimal: {decimal_dict}")
        return decimal_dict

    try:
        scale = decimal_dict['scale']
        value_bytes = decimal_dict['value']
        logging.debug(f"Decoding VariableScaleDecimal with scale {scale} and value bytes: {value_bytes.hex() if isinstance(value_bytes, bytes) else value_bytes}")

        if not isinstance(value_bytes, bytes):
            logging.debug(f"Value is not bytes, can't decode: {type(value_bytes)}")
            return decimal_dict

        int_value = int.from_bytes(value_bytes, byteorder="big", signed=True)
        logging.debug(f"Converted bytes to int: {int_value}")

        if scale == 0:
            logging.debug(f"Scale is 0, returning int: {int_value}")
            return int_value
        else:
            decimal_value = Decimal(int_value) / (10 ** scale)
            logging.debug(f"Converted to Decimal with scale {scale}: {decimal_value}")
            return decimal_value
    except Exception as e:
        logging.error(f"Failed to decode VariableScaleDecimal: {e}")
        return f"Decimal({decimal_dict.get('value', '').hex() if isinstance(decimal_dict.get('value'), bytes) else decimal_dict.get('value')}, scale={decimal_dict.get('scale')})"

def convert_decimal_fields(record: dict, decimal_fields: dict) -> dict:
    """
    Convert decimal fields from bytes to Decimal objects or properly formatted values
    """
    logging.debug(f"Converting decimal fields. Known decimal fields: {list(decimal_fields.keys())}")

    for field_name, info in decimal_fields.items():
        if field_name in record:
            original_value = record[field_name]
            logging.debug(f"Processing decimal field '{field_name}': {type(original_value)}")

            if isinstance(original_value, bytes):
                record[field_name] = decode_avro_decimal(original_value, info["scale"])
                logging.debug(f"  Converted bytes to {type(record[field_name])}: {record[field_name]}")
            elif isinstance(original_value, dict) and 'value' in original_value and isinstance(original_value['value'], bytes):
                record[field_name] = decode_avro_decimal(original_value['value'], original_value.get('scale', info["scale"]))
                logging.debug(f"  Converted dict with bytes to {type(record[field_name])}: {record[field_name]}")

    for field_name, value in list(record.items()):
        if isinstance(value, dict) and 'scale' in value and 'value' in value and isinstance(value['value'], bytes):
            logging.debug(f"Found potential VariableScaleDecimal in field '{field_name}': {value}")
            try:
                original_value = record[field_name]
                record[field_name] = decode_variable_scale_decimal(value)
                logging.debug(f"  Converted VariableScaleDecimal from {type(original_value)} to {type(record[field_name])}: {record[field_name]}")
            except Exception as e:
                logging.error(f"Error decoding VariableScaleDecimal {field_name}: {e}")

    return record

def convert_date_fields(record, schema_dict):
    """
    Convert date and timestamp fields to appropriate PostgreSQL formats.
    """
    logging.debug(f"Converting date fields in record with keys: {list(record.keys())}")

    for field in schema_dict.get('fields', []):
        field_name = field.get('name')
        field_type = field.get('type')

        if field_name not in record or record[field_name] is None:
            continue

        logging.debug(f"Checking field '{field_name}' with type: {field_type}")

        if isinstance(field_type, list):
            for type_option in field_type:
                if isinstance(type_option, dict) and 'logicalType' in type_option:
                    logical_type = type_option.get('logicalType')
                    logging.debug(f"  Found logical type in union: {logical_type}")

                    if logical_type == 'date' and isinstance(record[field_name], int):
                        original_value = record[field_name]
                        record[field_name] = (datetime(1970, 1, 1) + timedelta(days=record[field_name])).strftime('%Y-%m-%d')
                        logging.debug(f"  Converted date from {original_value} to {record[field_name]}")
                    elif logical_type == 'timestamp-millis' and isinstance(record[field_name], int):
                        original_value = record[field_name]
                        record[field_name] = datetime.fromtimestamp(record[field_name] / 1000).strftime('%Y-%m-%d %H:%M:%S')
                        logging.debug(f"  Converted timestamp from {original_value} to {record[field_name]}")

        elif isinstance(field_type, dict) and 'logicalType' in field_type:
            logical_type = field_type.get('logicalType')
            logging.debug(f"  Found direct logical type: {logical_type}")

            if logical_type == 'date' and isinstance(record[field_name], int):
                original_value = record[field_name]
                record[field_name] = (datetime(1970, 1, 1) + timedelta(days=record[field_name])).strftime('%Y-%m-%d')
                logging.debug(f"  Converted date from {original_value} to {record[field_name]}")
            elif logical_type == 'timestamp-millis' and isinstance(record[field_name], int):
                original_value = record[field_name]
                record[field_name] = datetime.fromtimestamp(record[field_name] / 1000).strftime('%Y-%m-%d %H:%M:%S')
                logging.debug(f"  Converted timestamp from {original_value} to {record[field_name]}")

    return record

def process_message(msg_value: bytes, avro_schema: dict) -> Dict:
    """
    Process a single message using the provided Avro schema
    """
    logging.debug(f"Processing message with {len(msg_value)} bytes")

    parsed_schema, decimal_fields = parse_schema_with_decimal(avro_schema)
    logging.debug(f"Parsed schema and found {len(decimal_fields)} decimal fields")

    bytes_reader = io.BytesIO(msg_value)
    record = fastavro.schemaless_reader(bytes_reader, parsed_schema)
    logging.debug(f"Decoded Avro message with {len(record)} fields: {list(record.keys())}")

    record = convert_decimal_fields(record, decimal_fields)
    logging.debug("Decimal fields converted")

    record = convert_date_fields(record, avro_schema)
    logging.debug("Date fields converted")

    sample_fields = {k: str(v)[:100] for k, v in list(record.items())[:5]}
    logging.debug(f"Processed record sample (first 5 fields): {sample_fields}")

    return record
