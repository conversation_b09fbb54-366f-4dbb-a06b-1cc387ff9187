from contextlib import contextmanager
from typing import Dict, Any, List, Optional, Generator
import logging
import psycopg2
from psycopg2.pool import Simple<PERSON>onnectionPool
from psycopg2.extras import execute_values

class DatabaseManager:
    def __init__(self, connection_string: str, min_conn: int = 1, max_conn: int = 10):
        self.connection_string = connection_string
        self.pool = SimpleConnectionPool(min_conn, max_conn, connection_string)
    
    @contextmanager
    def get_connection(self) -> Generator[psycopg2.extensions.connection, None, None]:
        conn = self.pool.getconn()
        try:
            yield conn
        finally:
            self.pool.putconn(conn)
    
    def close(self) -> None:
        if not self.pool.closed:
            self.pool.closeall()
    
    def get_table_columns(self, schema: str, table: str) -> List[str]:
        """Get the column names for a table"""
        query = """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = %s
            ORDER BY ordinal_position
        """
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(query, (schema, table))
                return [row[0] for row in cur.fetchall()]
    
    def insert_batch(self, schema: str, table: str, columns: List[str], records: List[Dict[str, Any]]) -> bool:
        """Insert a batch of records into the database"""
        if not records:
            return True
            
        # Prepare data for insertion
        values = []
        for record in records:
            row = [record.get(col) for col in columns]
            values.append(row)
        
        # Build the query
        columns_str = ", ".join(f'"{col}"' for col in columns)
        query = f'INSERT INTO "{schema}"."{table}" ({columns_str}) VALUES %s'
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    execute_values(cur, query, values)
                conn.commit()
            return True
        except Exception as e:
            logging.error(f"Database error: {e}")
            return False